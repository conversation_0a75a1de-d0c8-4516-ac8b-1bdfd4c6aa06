package io.securinest.controlplaneapi.repository.policy;

import io.securinest.controlplaneapi.entity.policy.PolicyBundle;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;
import java.util.UUID;

public interface PolicyBundleRepository extends BaseTenantRepository<PolicyBundle, UUID> {

    Optional<PolicyBundle> findTopByTenantIdAndServiceIdAndEnvIdAndActiveTrueOrderByCreatedAtDesc(UUID tenantId, UUID serviceId, UUID envId);

    Page<PolicyBundle> findAllByTenantIdAndServiceIdAndEnvId(UUID tenantId, UUID serviceId, UUID envId, Pageable pageable);

    Optional<PolicyBundle> findByTenantIdAndIdAndEtag(UUID tenantId, UUID id, String etag);

    @Query("""
            update PolicyBundle b
            set b.active = false
            where b.tenantId = :tenantId
            and b.serviceId = :serviceId
            and b.envId = :envId
            and b.active = true""")
    @Modifying
    int deactivatePrevious(UUID tenantId, UUID serviceId, UUID envId);

    @Query("""
            update PolicyBundle b
            set b.active = false
            where b.tenantId = :tenantId
            and b.id = :bundleId
            and b.active = true""")
    @Modifying
    int deactivateBundle(UUID tenantId, UUID bundleId);

}
