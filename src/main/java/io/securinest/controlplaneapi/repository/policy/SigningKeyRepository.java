package io.securinest.controlplaneapi.repository.policy;

import io.securinest.controlplaneapi.entity.policy.SigningKey;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface SigningKeyRepository extends BaseTenantRepository<SigningKey, UUID> {

    Optional<SigningKey> findByTenantIdAndKid(UUID tenantId, String kid);

    List<SigningKey> findAllByTenantIdAndRevokedAtIsNull(UUID tenantId);

}
