package io.securinest.controlplaneapi.repository.policy;

import io.securinest.controlplaneapi.entity.policy.CompliancePolicyVersion;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;
import java.util.UUID;

public interface CompliancePolicyVersionRepository extends JpaRepository<CompliancePolicyVersion, UUID> {

    Page<CompliancePolicyVersion> findAllByPolicyIdOrderByVersionNoDesc(UUID policyId, Pageable pageable);

    Optional<CompliancePolicyVersion> findByPolicyIdAndVersionNo(UUID policyId, int versionNo);

    Optional<CompliancePolicyVersion> findTopByPolicyIdAndStatusOrderByVersionNoDesc(UUID policyId, PolicyState status);

    @Query("""
            select coalesce(max(v.versionNo),0)
            from CompliancePolicyVersion v
            where v.policyId = :policyId""")
    int maxVersion(UUID policyId);

    @Query("""
            update CompliancePolicyVersion v
            set v.status = :newStatus
            where v.policyId = :policyId
            and v.status = :oldStatus""")
    @Modifying
    int updateStatusByPolicyIdAndStatus(UUID policyId, PolicyState oldStatus, PolicyState newStatus);

}
