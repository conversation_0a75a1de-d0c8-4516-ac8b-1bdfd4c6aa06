package io.securinest.controlplaneapi.repository.scm;

import io.securinest.controlplaneapi.entity.scm.PolicySuggestion;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface PolicySuggestionRepository extends BaseTenantRepository<PolicySuggestion, UUID> {

    Page<PolicySuggestion> findAllByTenantIdOrderByCreatedAtDesc(UUID tenantId, Pageable pageable);

}
