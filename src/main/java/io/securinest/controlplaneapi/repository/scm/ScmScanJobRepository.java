package io.securinest.controlplaneapi.repository.scm;

import io.securinest.controlplaneapi.entity.scm.ScmScanJob;
import io.securinest.controlplaneapi.entity.shared.JobStatus;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface ScmScanJobRepository extends BaseTenantRepository<ScmScanJob, UUID> {

    Page<ScmScanJob> findAllByTenantIdOrderByStartedAtDesc(UUID tenantId, Pageable pageable);

    Page<ScmScanJob> findAllByTenantIdAndStatus(UUID tenantId, JobStatus status, Pageable pageable);

}
