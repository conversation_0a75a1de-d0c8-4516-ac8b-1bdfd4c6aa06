package io.securinest.controlplaneapi.repository.scm;

import io.securinest.controlplaneapi.entity.scm.ScmAccount;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.UUID;

public interface ScmAccountRepository extends BaseTenantRepository<ScmAccount, UUID> {

    Optional<ScmAccount> findByTenantIdAndProviderAndExternalInstallationId(UUID tenantId, String provider, String externalInstallationId);

    Page<ScmAccount> findAllByTenantId(UUID tenantId, Pageable pageable);

}
