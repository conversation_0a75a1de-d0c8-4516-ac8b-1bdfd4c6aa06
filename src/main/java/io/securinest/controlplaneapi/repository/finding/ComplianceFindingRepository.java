package io.securinest.controlplaneapi.repository.finding;

import io.securinest.controlplaneapi.entity.finding.ComplianceFinding;
import io.securinest.controlplaneapi.entity.shared.FindingStatus;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.UUID;

public interface ComplianceFindingRepository extends BaseTenantRepository<ComplianceFinding, UUID> {

    Page<ComplianceFinding> findAllByTenantIdAndStatus(UUID tenantId, FindingStatus status, Pageable pageable);

    Page<ComplianceFinding> findAllByServiceIdAndEnvIdOrderByLastSeenDesc(UUID serviceId, UUID envId, Pageable pageable);

    @Query("""
            select count(f)
            from ComplianceFinding f
            where f.tenantId = :tenantId
            and f.status = 'OPEN'""")
    long countOpen(UUID tenantId);

}
