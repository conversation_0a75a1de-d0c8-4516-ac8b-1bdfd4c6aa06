package io.securinest.controlplaneapi.repository.usage;

import io.securinest.controlplaneapi.entity.usage.UsageDaily;
import io.securinest.controlplaneapi.entity.usage.UsageDailyId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

public interface UsageDailyRepository extends JpaRepository<UsageDaily, UsageDailyId> {

    List<UsageDaily> findAllByIdTenantIdAndIdDayBetween(UUID tenantId, LocalDate from, LocalDate to);

    List<UsageDaily> findAllByIdTenantIdAndIdServiceIdAndIdEnvIdAndIdDayBetween(UUID tenantId, UUID serviceId, UUID envId, LocalDate from, LocalDate to);

    @Query(
            value = """
                    insert into usage_daily(tenant_id, service_id, env_id, day, events_count, violations_count, bytes_out)
                    values (:tenantId, :serviceId, :envId, :day, :events, :violations, :bytes)
                    on conflict (tenant_id, service_id, env_id, day)
                    do update set
                       events_count = usage_daily.events_count + excluded.events_count,
                       violations_count = usage_daily.violations_count + excluded.violations_count,
                       bytes_out = usage_daily.bytes_out + excluded.bytes_out""",
            nativeQuery = true
    )
    @Modifying
    int upsertAdd(UUID tenantId, UUID serviceId, UUID envId, LocalDate day, long events, long violations, long bytes);

}
