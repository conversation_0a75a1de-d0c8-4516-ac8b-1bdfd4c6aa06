package io.securinest.controlplaneapi.repository.tenant;

import io.securinest.controlplaneapi.entity.tenant.TenantDomain;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface TenantDomainRepository extends BaseTenantRepository<TenantDomain, UUID> {

    Optional<TenantDomain> findByDomain(String domain);

    boolean existsByDomain(String domain);

    List<TenantDomain> findAllByTenantIdAndVerifiedTrue(UUID tenantId);

}
