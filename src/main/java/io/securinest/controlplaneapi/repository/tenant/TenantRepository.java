package io.securinest.controlplaneapi.repository.tenant;

import io.securinest.controlplaneapi.entity.tenant.Tenant;
import io.securinest.controlplaneapi.projection.tenant.TenantWithRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.Optional;
import java.util.UUID;

public interface TenantRepository extends JpaRepository<Tenant, UUID> {

    Optional<Tenant> findBySlug(String slug);

    boolean existsBySlug(String slug);

    boolean existsBySlugIgnoreCase(String slug);

    @Query(
  """
  select t, m.role
  from Tenant t
  join TenantMember m on m.tenantId = t.id and m.userId = :userId
  where t.id = :tenantId
  """
    )
    Optional<TenantWithRole> findTenantAndRole(UUID tenantId, UUID userId);

    Page<Tenant> findAllByIdIn(Collection<UUID> ids, Pageable pageable);

    @Query(
            value = """
    select distinct t
    from Tenant t
    join TenantMember m on m.tenantId = t.id
    where m.userId = :userId
  """,
            countQuery = """
    select count(distinct t.id)
    from Tenant t
    join TenantMember m on m.tenantId = t.id
    where m.userId = :userId
  """
    )
    Page<Tenant> findPageByMemberUserId(@Param("userId") UUID userId, Pageable pageable);

    @Query("""
      select t from Tenant t
      where t.id = :tenantId
        and exists (
          select 1 from TenantMember m
          where m.tenantId = t.id and m.userId = :userId
        )
    """)
    Optional<Tenant> findByIdIfMember(@Param("tenantId") UUID tenantId, @Param("userId") UUID userId);
}
