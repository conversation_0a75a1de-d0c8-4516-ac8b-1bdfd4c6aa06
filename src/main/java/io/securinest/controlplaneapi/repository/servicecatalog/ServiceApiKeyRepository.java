package io.securinest.controlplaneapi.repository.servicecatalog;

import io.securinest.controlplaneapi.entity.servicecatalog.ServiceApiKey;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

public interface ServiceApiKeyRepository extends BaseTenantRepository<ServiceApiKey, UUID> {

    Optional<ServiceApiKey> findByPrefix(String prefix);

    Page<ServiceApiKey> findAllByTenantIdAndServiceId(UUID tenantId, UUID serviceId, Pageable pageable);

    @Query("""
            update ServiceApiKey k
            set k.lastUsedAt = :ts
            where k.id = :id""")
    @Modifying
    int touchLastUsed(UUID id, Instant ts);


    @Query("""
            update ServiceApiKey k
            set k.revokedAt = :ts
            where k.tenantId = :tenantId
            and k.id = :id
            and k.revokedAt is null""")
    @Modifying
    int revoke(UUID tenantId, UUID id, Instant ts);

}
