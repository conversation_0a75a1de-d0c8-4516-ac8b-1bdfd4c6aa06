package io.securinest.controlplaneapi.repository.inventory;

import io.securinest.controlplaneapi.entity.inventory.ObservedEndpoint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

public interface ObservedEndpointRepository extends JpaRepository<ObservedEndpoint, UUID> {

    Optional<ObservedEndpoint> findByServiceIdAndEnvIdAndHostAndPath(UUID serviceId, UUID envId, String host, String path);

    Page<ObservedEndpoint> findAllByServiceIdAndEnvIdOrderByLastSeenDesc(UUID serviceId, UUID envId, Pageable pageable);

}
