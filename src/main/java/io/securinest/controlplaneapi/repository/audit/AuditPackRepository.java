package io.securinest.controlplaneapi.repository.audit;

import io.securinest.controlplaneapi.entity.audit.AuditPack;
import io.securinest.controlplaneapi.entity.shared.JobStatus;
import io.securinest.controlplaneapi.repository.shared.BaseTenantRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.util.UUID;

public interface AuditPackRepository extends BaseTenantRepository<AuditPack, UUID> {

    Page<AuditPack> findAllByTenantIdOrderByCreatedAtDesc(UUID tenantId, Pageable pageable);

    @Query("""
            update AuditPack a
            set
               a.status = :status,
               a.manifestHash = :manifestHash,
               a.artifactUri = :artifactUri,
               a.signatureB64 = :signature,
               a.finishedAt = :finishedAt
            where a.id = :id and a.tenantId = :tenantId"""
    )
    @Modifying
    int updateStatus(UUID tenantId, UUID id, JobStatus status, String manifestHash, String artifactUri, String signature, Instant finishedAt);

}
