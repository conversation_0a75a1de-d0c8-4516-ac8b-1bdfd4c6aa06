package io.securinest.controlplaneapi.repository.audit;

import io.securinest.controlplaneapi.entity.audit.AuditLogEntry;
import io.securinest.controlplaneapi.entity.shared.AuditAction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.Instant;
import java.util.Collection;
import java.util.UUID;

public interface AuditLogEntryRepository extends JpaRepository<AuditLogEntry, Long> {

    Page<AuditLogEntry> findAllByTenantIdAndTsBetweenOrderByTsDesc(UUID tenantId, Instant from, Instant to, Pageable pageable);

    Page<AuditLogEntry> findAllByTenantIdAndActionInOrderByTsDesc(UUID tenantId, Collection<AuditAction> actions, Pageable pageable);

}
