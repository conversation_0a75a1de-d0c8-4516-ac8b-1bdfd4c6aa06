package io.securinest.controlplaneapi.util.policy;

import lombok.experimental.UtilityClass;

@UtilityClass
public class PolicyUtils {

    public static Long parseIfMatch(String ifMatch) {
        if (ifMatch == null) return null;
        for (String token : ifMatch.split(",")) {
            String value = token.trim();
            if (value.startsWith("W/")) value = value.substring(2).trim();
            if (value.startsWith("\"") && value.endsWith("\"")) value = value.substring(1, value.length()-1);
            try { return Long.parseLong(value); } catch (NumberFormatException ignored) {}
        }
        return null;
    }
}
