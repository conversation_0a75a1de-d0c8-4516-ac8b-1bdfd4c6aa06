package io.securinest.controlplaneapi.util.shared;

import io.securinest.controlplaneapi.dto.shared.VersionedResource;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.security.MessageDigest;

@UtilityClass
public class ResponseEntityUtils {

    public static <T extends VersionedResource> ResponseEntity<T> updateResponse(T res) {
        return ResponseEntity.ok()
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .eTag(strongEtagFromVersion(res.version()))
                .body(res);
    }

    public static <T extends VersionedResource> ResponseEntity<T> createResponse(T res) {
        URI location = ServletUriComponentsBuilder.fromCurrentRequest()
                .path("/{id}")
                .buildAndExpand(res.id())
                .toUri();

        return ResponseEntity.created(location)
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .eTag(strongEtagFromVersion(res.version()))
                .body(res);
    }

    public static <T extends VersionedResource> ResponseEntity<T> getResponse(T res) {
        return ResponseEntity.ok()
                .header(HttpHeaders.CACHE_CONTROL, Constants.DEFAULT_CACHE_CONTROL)
                .eTag(strongEtagFromVersion(res.version()))
                .body(res);
    }

    public static <T extends VersionedResource> ResponseEntity<T> getResponseWeak(T res) {
        return ResponseEntity.ok()
                .header(HttpHeaders.CACHE_CONTROL, Constants.DEFAULT_CACHE_CONTROL)
                .eTag(weakEtag("v" + res.version()))
                .body(res);
    }

    public static ResponseEntity<Void> deleteNoContent() {
        return ResponseEntity.noContent()
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .build();
    }

    public static <T> ResponseEntity<T> ok(T body) {
        return ResponseEntity.ok()
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .body(body);
    }

    public static <T> ResponseEntity<T> created(T body) {
        return ResponseEntity.status(201)
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .body(body);
    }

    public static <T> ResponseEntity<T> preconditionRequired(Class<T> responseType) {
        return ResponseEntity.status(HttpStatus.PRECONDITION_REQUIRED).build();
    }

    public static <T> ResponseEntity<T> preconditionFailed(Class<T> responseType) {
        return ResponseEntity.status(HttpStatus.PRECONDITION_FAILED).build();
    }

    public static <T> ResponseEntity<T> notModified(Class<T> responseType) {
        return ResponseEntity.status(HttpStatus.NOT_MODIFIED).build();
    }

    public static <T> ResponseEntity<T> statusWithHeaders(HttpStatus status, String etag, String cacheControl) {
        ResponseEntity.BodyBuilder builder = ResponseEntity.status(status);

        if (etag != null) {
            builder.eTag(etag);
        }

        if (cacheControl != null) {
            builder.header(HttpHeaders.CACHE_CONTROL, cacheControl);
        }

        return builder.build();
    }

    public static <T> ResponseEntity<T> notModified(String etag, String cacheControl) {
        return statusWithHeaders(HttpStatus.NOT_MODIFIED, etag, cacheControl);
    }

    public static <T> ResponseEntity<T> notModified(String etag) {
        return notModified(etag, Constants.DEFAULT_CACHE_CONTROL);
    }

    public static String strongEtagFromVersion(Integer version) {
        return "\"v" + version + "\"";
    }

    // will be used in GET /sdk/bundles/current
    public static String strongEtagFromSha256(byte[] canonicalBytes) {
        return "\"" + sha256Hex(canonicalBytes) + "\"";
    }

    public static String weakEtag(String token) {
        return "W/\"" + token + "\"";
    }

    private static String sha256Hex(byte[] bytes) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(bytes);
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 not available", e);
        }
    }
}

