package io.securinest.controlplaneapi.util.shared;

import lombok.experimental.UtilityClass;

import java.util.regex.Pattern;

@UtilityClass
public class Constants {

    public static final String MESSAGE = "message";
    public static final String STATUS = "status";
    public static final String ERROR = "error";

    public static final int NAME_MIN_LENGTH = 3;
    public static final int NAME_MAX_LENGTH = 200;
    public static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    public static final Pattern SLUG_PATTERN = Pattern.compile("^(?!-)(?!.*--)[a-z0-9-]{3,120}(?<!-)$");
    public static final Pattern SLUG_HAS_LETTER = Pattern.compile(".*[a-z].*");
    public static final int SLUG_MIN_LENGTH = 3;
    public static final int SLUG_MAX_LENGTH = 120;

    public static final String DEFAULT_CACHE_CONTROL = "private, max-age=60, no-transform";
}
