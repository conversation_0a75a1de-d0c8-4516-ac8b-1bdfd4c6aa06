package io.securinest.controlplaneapi.exceptions.shared;

import io.securinest.controlplaneapi.dto.shared.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Slf4j
public class ExceptionHandling {

    @ExceptionHandler(SecurinestException.class)
    public ResponseEntity<ErrorResponse> handleSecurinestException(SecurinestException ex) {
        log.warn("SecurinestException: {} - {}", ex.getHttpStatus(), ex.getMessage());
        
        ErrorResponse error = ErrorResponse.builder()
                .code("SECURINEST_ERROR")
                .message(ex.getMessage())
                .build();
        
        return ResponseEntity.status(ex.getHttpStatus()).body(error);
    }

    @ExceptionHandler(SecurinestConfigurationException.class)
    public ResponseEntity<ErrorResponse> handleConfigurationException(SecurinestConfigurationException ex) {
        log.error("Configuration error: {}", ex.getMessage(), ex);
        
        ErrorResponse error = ErrorResponse.builder()
                .code("CONFIGURATION_ERROR")
                .message("System configuration error")
                .build();
        
        return ResponseEntity.status(500).body(error);
    }
}
