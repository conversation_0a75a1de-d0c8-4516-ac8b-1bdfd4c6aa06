package io.securinest.controlplaneapi.exceptions.shared;

import java.io.Serial;

public class SecurinestConfigurationException extends IllegalStateException {
    @Serial
    private static final long serialVersionUID = 1L;

    public SecurinestConfigurationException(String message) {
        super(message);
    }

    public static SecurinestConfigurationException forKey(String key, String detail) {
        String msg = (detail == null || detail.isBlank())
                ? "Misconfiguration: " + key
                : "Misconfiguration: " + key + " — " + detail;
        return new SecurinestConfigurationException(msg);
    }
}