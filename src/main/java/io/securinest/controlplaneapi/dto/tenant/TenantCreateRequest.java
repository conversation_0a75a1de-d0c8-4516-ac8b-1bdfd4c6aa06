package io.securinest.controlplaneapi.dto.tenant;

import io.securinest.controlplaneapi.validation.AllowedRegion;
import io.securinest.controlplaneapi.validation.ValidSlug;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Builder;

@Builder
public record TenantCreateRequest(
        @NotBlank(message = "Name is required")
        @Size(min = 3, max = 120, message = "Name must be between 3 and 120 characters")
        String name,

        @NotBlank(message = "Slug is required")
        @ValidSlug
        String slug,

        @NotBlank(message = "Region is required")
        @AllowedRegion
        String region,

        @Size(max = 50, message = "Billing plan must not exceed 50 characters")
        String billingPlan
) {
}
