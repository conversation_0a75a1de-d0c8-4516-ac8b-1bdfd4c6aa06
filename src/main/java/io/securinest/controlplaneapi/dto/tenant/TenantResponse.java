package io.securinest.controlplaneapi.dto.tenant;

import io.securinest.controlplaneapi.dto.shared.VersionedResource;
import lombok.Builder;

import java.time.Instant;

@Builder
public record TenantResponse(
        String id,
        String name,
        String slug,
        String billingPlan,
        String region,
        Instant createdAt,
        Instant updatedAt,
        Integer version
) implements VersionedResource {
}
