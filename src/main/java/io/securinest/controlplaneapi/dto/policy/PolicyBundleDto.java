package io.securinest.controlplaneapi.dto.policy;

import lombok.Builder;

import java.time.Instant;

@Builder
public record PolicyBundleDto(
        String id,
        String tenantId,
        String policyVersionId,
        String serviceId,
        String envId,
        String eTag,
        String kid,
        boolean active,
        String enforcementMode,
        Integer lkgTtlSeconds,
        String sampling,
        Instant createdAt,
        Instant updatedAt,
        Long version
) {
}
