package io.securinest.controlplaneapi.dto.ingestion;

import lombok.Builder;

import java.time.Instant;
import java.util.Map;

@Builder
public record TelemetryEventResponse(
        String id,
        String tenantId,
        String serviceId,
        String envId,
        Instant ts,
        String type,
        String endpoint,
        String policyVer,
        String policyHash,
        Boolean violation,
        Long latencyMs,
        Map<String, Object> payload,
        Instant createdAt
) {
}
