package io.securinest.controlplaneapi.dto.audit;

import lombok.Builder;

import java.time.Instant;

@Builder
public record AuditPackResponse(
        String id,
        String tenantId,
        AuditPackScopeResponse scope,
        String status,
        String manifestHash,
        String artifactUri,
        String aiActVersion,
        String createdBy,
        Instant createdAt,
        Instant finishedAt,
        String signatureB64
) {
}
