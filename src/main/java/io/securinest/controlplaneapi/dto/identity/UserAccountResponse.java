package io.securinest.controlplaneapi.dto.identity;

import io.securinest.controlplaneapi.dto.shared.VersionedResource;
import lombok.Builder;

import java.time.Instant;

@Builder
public record UserAccountResponse(
        String id,
        String kcSub,
        String email,
        String displayName,
        Instant createdAt,
        Instant updatedAt,
        Integer version
) implements VersionedResource {
}
