package io.securinest.controlplaneapi.entity.scm;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import io.securinest.controlplaneapi.entity.shared.JobStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.Map;

@Entity
@Table(name = "scm_scan_job")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ScmScanJob extends AbstractTenantEntity {

    @Column(name = "triggered_by")
    private java.util.UUID triggeredBy;

    @NotNull
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "scope", nullable = false, columnDefinition = "jsonb")
    private Map<String, Object> scope;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private JobStatus status;

    @Column(name = "started_at")
    private Instant startedAt;

    @Column(name = "finished_at")
    private Instant finishedAt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "counters", columnDefinition = "jsonb")
    private Map<String, Object> counters;

}
