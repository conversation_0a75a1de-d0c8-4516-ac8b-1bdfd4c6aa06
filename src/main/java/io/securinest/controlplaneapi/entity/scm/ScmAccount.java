package io.securinest.controlplaneapi.entity.scm;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "scm_account")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ScmAccount extends AbstractTenantEntity {

    @NotBlank
    @Pattern(regexp = "github|gitlab|bitbucket")
    @Column(name = "provider", nullable = false)
    private String provider;

    @NotBlank
    @Size(max = 120)
    @Column(name = "external_installation_id", nullable = false, length = 120)
    private String externalInstallationId;

    @Size(max = 255)
    @Column(name = "app_slug")
    private String appSlug;

    @NotBlank
    @Column(name = "webhook_secret", nullable = false)
    private String webhookSecret;

    @NotBlank
    @Column(name = "status", nullable = false)
    private String status;

}
