package io.securinest.controlplaneapi.entity.tenant;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "tenant_domain")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TenantDomain extends AbstractTenantEntity {

    @NotBlank
    @Pattern(regexp = "^[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")
    @Column(name = "domain", nullable = false, unique = true)
    private String domain;

    @NotNull
    @Column(name = "verified", nullable = false)
    private boolean verified = false;

    @Size(max = 120)
    @Column(name = "txt_value", length = 120)
    private String txtValue;

}
