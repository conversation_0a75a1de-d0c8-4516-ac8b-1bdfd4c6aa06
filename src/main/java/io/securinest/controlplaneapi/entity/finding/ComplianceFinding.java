package io.securinest.controlplaneapi.entity.finding;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import io.securinest.controlplaneapi.entity.shared.FindingSeverity;
import io.securinest.controlplaneapi.entity.shared.FindingStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "compliance_finding")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ComplianceFinding extends AbstractTenantEntity {

    @NotNull
    @Column(name = "service_id", nullable = false)
    private UUID serviceId;

    @NotNull
    @Column(name = "env_id", nullable = false)
    private UUID envId;

    @NotBlank
    @Size(max = 80)
    @Column(name = "code", nullable = false, length = 80)
    private String code;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "severity", nullable = false)
    private FindingSeverity severity;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private FindingStatus status = FindingStatus.OPEN;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private Map<String, Object> details;

    @NotNull
    @Column(name = "first_seen", nullable = false)
    private Instant firstSeen;

    @NotNull
    @Column(name = "last_seen", nullable = false)
    private Instant lastSeen;

}
