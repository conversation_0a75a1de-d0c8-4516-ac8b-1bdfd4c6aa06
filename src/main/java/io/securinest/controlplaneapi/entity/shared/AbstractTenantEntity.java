package io.securinest.controlplaneapi.entity.shared;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@MappedSuperclass
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public abstract class AbstractTenantEntity extends AbstractEntity {

    @Column(name = "tenant_id", nullable = false)
    private UUID tenantId;

}
