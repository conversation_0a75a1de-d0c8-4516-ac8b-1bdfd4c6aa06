package io.securinest.controlplaneapi.entity.servicecatalog;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "environment")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Environment extends AbstractTenantEntity {

    @NotBlank
    @Pattern(regexp = "^[a-z0-9-]{2,32}$")
    @Column(name = "key", nullable = false, length = 32)
    private String key;

    @NotBlank
    @Size(max = 64)
    @Column(name = "name", nullable = false, length = 64)
    private String name;

}
