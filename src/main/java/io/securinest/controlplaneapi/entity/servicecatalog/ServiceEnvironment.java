package io.securinest.controlplaneapi.entity.servicecatalog;

import io.securinest.controlplaneapi.entity.shared.AbstractEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Entity
@Table(name = "service_environment")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ServiceEnvironment extends AbstractEntity {

    @NotNull
    @Column(name = "service_id", nullable = false)
    private UUID serviceId;

    @NotNull
    @Column(name = "env_id", nullable = false)
    private UUID envId;

}
