package io.securinest.controlplaneapi.entity.usage;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;
import java.util.UUID;

@Embeddable
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class UsageDailyId implements Serializable {

    @Column(name = "tenant_id", nullable = false)
    private UUID tenantId;

    @Column(name = "service_id", nullable = false)
    private UUID serviceId;

    @Column(name = "env_id", nullable = false)
    private UUID envId;

    @Column(name = "day", nullable = false)
    private LocalDate day;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        UsageDailyId that = (UsageDailyId) o;

        return Objects.equals(tenantId, that.tenantId) &&
                Objects.equals(serviceId, that.serviceId) &&
                Objects.equals(envId, that.envId) &&
                Objects.equals(day, that.day);
    }

    @Override
    public int hashCode() { return Objects.hash(tenantId, serviceId, envId, day); }

}
