package io.securinest.controlplaneapi.entity.usage;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "usage_daily")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class UsageDaily {

    @EmbeddedId
    private UsageDailyId id;

    @NotNull
    @Column(name = "events_count", nullable = false)
    private Long eventsCount = 0L;

    @NotNull
    @Column(name = "violations_count", nullable = false)
    private Long violationsCount = 0L;

    @NotNull
    @Column(name = "bytes_out", nullable = false)
    private Long bytesOut = 0L;

}
