package io.securinest.controlplaneapi.entity.inventory;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "observed_endpoint")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ObservedEndpoint extends AbstractTenantEntity {

    @NotNull
    @Column(name = "service_id", nullable = false)
    private UUID serviceId;

    @NotNull
    @Column(name = "env_id", nullable = false)
    private UUID envId;

    @NotBlank
    @Size(max = 200)
    @Column(name = "host", nullable = false, length = 200)
    private String host;

    @NotBlank
    @Size(max = 600)
    @Column(name = "path", nullable = false, length = 600)
    private String path;

    @NotNull
    @Column(name = "first_seen", nullable = false)
    private Instant firstSeen;

    @NotNull
    @Column(name = "last_seen", nullable = false)
    private Instant lastSeen;

    @NotNull
    @Column(name = "calls", nullable = false)
    private Long calls = 0L;

    @Column(name = "last_status")
    private Integer lastStatus;

}
