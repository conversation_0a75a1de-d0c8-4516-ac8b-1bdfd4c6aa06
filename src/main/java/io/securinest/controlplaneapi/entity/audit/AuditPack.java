package io.securinest.controlplaneapi.entity.audit;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import io.securinest.controlplaneapi.entity.shared.JobStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "audit_pack")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class AuditPack extends AbstractTenantEntity {

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "scope", nullable = false, columnDefinition = "jsonb")
    private Map<String, Object> scope;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private JobStatus status;

    @Size(max = 80)
    @Column(name = "manifest_hash", length = 80)
    private String manifestHash;

    @Column(name = "artifact_uri")
    private String artifactUri;

    @Column(name = "ai_act_version")
    private String aiActVersion;

    @Column(name = "created_by")
    private UUID createdBy;

    @Column(name = "finished_at")
    private Instant finishedAt;

    @Column(name = "signature_b64")
    private String signatureB64;

}

