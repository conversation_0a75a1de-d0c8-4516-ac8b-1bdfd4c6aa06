package io.securinest.controlplaneapi.entity.policy;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Entity
@Table(name = "compliance_policy")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class CompliancePolicy extends AbstractTenantEntity {

    @NotBlank
    @Size(min = 3, max = 200)
    @Column(name = "name", nullable = false, length = 200)
    private String name;

    @Size(max = 2000)
    @Column(name = "description", length = 2000)
    private String description;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private PolicyState state = PolicyState.DRAFT;

    @Column(name = "created_by")
    private UUID createdBy;

}
