package io.securinest.controlplaneapi.service;


import io.securinest.controlplaneapi.dto.shared.PageResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantCreateRequest;
import io.securinest.controlplaneapi.dto.tenant.TenantResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantUpdateRequest;
import io.securinest.controlplaneapi.entity.shared.Role;
import io.securinest.controlplaneapi.entity.tenant.Tenant;
import io.securinest.controlplaneapi.entity.tenant.TenantMember;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import io.securinest.controlplaneapi.mapper.tenant.TenantMapper;
import io.securinest.controlplaneapi.projection.tenant.TenantChangeSet;
import io.securinest.controlplaneapi.projection.tenant.TenantPatch;
import io.securinest.controlplaneapi.projection.tenant.TenantWithRole;
import io.securinest.controlplaneapi.repository.tenant.TenantMemberRepository;
import io.securinest.controlplaneapi.repository.tenant.TenantRepository;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import io.securinest.controlplaneapi.util.tenant.TenantUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.Objects;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantService {

    private final TenantRepository tenantRepository;
    private final TenantMemberRepository tenantMemberRepository;
    private final TenantMapper tenantMapper;
    private final TenantRulebook tenantRulebook;



    @Transactional
    public TenantResponse createTenant(UUID currentUserId, TenantCreateRequest req, String requestId) {
        if (currentUserId == null) throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
        if (req == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");

        String normalizedName = tenantRulebook.normalizeName(req.name());
        String normalizedSlug = tenantRulebook.normalizeSlug(req.slug());
        String normalizedRegion = tenantRulebook.normalizeRegion(req.region());
        String normalizedBillingPlan = ValidationUtils.trimToNull(req.billingPlan());

        if (normalizedBillingPlan == null || normalizedBillingPlan.isBlank()) {
            normalizedBillingPlan = "free";
        }

        TenantPatch tenantToCreate = new TenantPatch(normalizedName, normalizedSlug, normalizedRegion, normalizedBillingPlan);

        if (tenantToCreate.name() != null) tenantRulebook.validateName(tenantToCreate.name(), "Name");
        if (tenantToCreate.slug() != null) tenantRulebook.validateSlug(tenantToCreate.slug());
        if (tenantToCreate.region() != null) tenantRulebook.validateRegion(tenantToCreate.region());

        if (normalizedSlug != null && tenantRepository.existsBySlugIgnoreCase(normalizedSlug)) {
            throw new SecurinestException(HttpStatus.CONFLICT, "Slug already taken");
        }

        Tenant tenant = TenantUtils.createFromPatch(tenantToCreate);

        try {
            tenantRepository.save(tenant);
        } catch (DataIntegrityViolationException e) {
            log.warn("createTenant duplicate slug race. requestId={} msg={}", requestId, e.getMessage());
            throw new SecurinestException(HttpStatus.CONFLICT, "Slug already taken");
        }

        TenantMember tenantMembership = new TenantMember();
        tenantMembership.setTenantId(tenant.getId());
        tenantMembership.setUserId(currentUserId);
        tenantMembership.setRole(Role.OWNER);
        tenantMemberRepository.save(tenantMembership);
        // TODO: Use @TransactionalEventListener(phase = AFTER_COMMIT) for domain events
        // TODO: Use TransactionSynchronizationManager for after-commit callbacks
        log.info("Tenant created: tenantId={} userId={} requestId={}", tenantMembership.getTenantId(), currentUserId, requestId);
        // TODO: Emit domain event TenantCreated for audit/observability after commit
        return tenantMapper.toResponse(tenant);
    }

    public TenantResponse getTenant(UUID tenantId, UUID currentUserId) {
        if (currentUserId == null) throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Missing current user");

        return tenantRepository.findByIdIfMember(tenantId, currentUserId)
                .map(tenantMapper::toResponse)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Tenant not found"));
    }

    public PageResponse<TenantResponse> listTenantsForUser(UUID currentUserId, int page, int size) {
        if (currentUserId == null) throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Missing current user");

        int safePage = Math.max(page, 0);
        int cappedSize = Math.min(size, 200);

        Pageable pageable = PageRequest.of(safePage, cappedSize);
        Page<TenantResponse> p = tenantRepository.findPageByMemberUserId(currentUserId, pageable)
                .map(tenantMapper::toResponse);

        return new PageResponse<>(
                p.getContent(),
                p.getNumber(),
                p.getSize(),
                p.getTotalElements(),
                p.getTotalPages()
        );
    }

    @Transactional
    public TenantResponse updateTenant(UUID tenantId, UUID requesterUserId, TenantUpdateRequest req, Integer ifMatchVersion, String requestId) {
        if (requesterUserId == null) throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Missing current user");
        if (req == null) throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Missing body");

        TenantWithRole tenantWithRole = tenantRepository.findTenantAndRole(tenantId, requesterUserId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Tenant not found"));

        Tenant tenant = tenantWithRole.tenant();
        Role role = tenantWithRole.role();

        if (!(role == Role.OWNER || role == Role.ADMIN))
            throw new SecurinestException(HttpStatus.FORBIDDEN, "Insufficient role");

        long expectedVersion = (ifMatchVersion != null) ? ifMatchVersion : ValidationUtils.defaultInt(req.version(), -1);

        if (expectedVersion < 0) throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Missing version");
        if (!Objects.equals(tenant.getVersion(), expectedVersion))
            throw new SecurinestException(HttpStatus.CONFLICT, "Version mismatch");

        String normalizedName = tenantRulebook.normalizeName(req.name());
        String normalizedSlug = tenantRulebook.normalizeSlug(req.slug());
        String normalizedRegion = tenantRulebook.normalizeRegion(req.region());
        String normalizedBillingPlan = ValidationUtils.trimToNull(req.billingPlan());

        TenantPatch tenantToUpdate = new TenantPatch(normalizedName, normalizedSlug, normalizedRegion, normalizedBillingPlan);

        if (tenantToUpdate.name() != null) tenantRulebook.validateName(tenantToUpdate.name(), "Name");
        if (tenantToUpdate.slug() != null) tenantRulebook.validateSlug(tenantToUpdate.slug());
        if (tenantToUpdate.region() != null) tenantRulebook.validateRegion(tenantToUpdate.region());


        if (tenantToUpdate.slug() != null && !tenantToUpdate.slug().equals(tenant.getSlug()) &&
            tenantRepository.existsBySlugIgnoreCase(tenantToUpdate.slug())) {
            throw new SecurinestException(HttpStatus.CONFLICT, "Slug already taken");
        }

        TenantChangeSet changeSet = TenantUtils.computeChangeSet(tenant, tenantToUpdate);

        if (!changeSet.hasChanges()) {
            return tenantMapper.toResponse(tenant);
        }

        TenantUtils.applyChangeSet(tenant, changeSet);

        try {
            Tenant savedTenant = tenantRepository.save(tenant);
            return tenantMapper.toResponse(savedTenant);
        } catch (ObjectOptimisticLockingFailureException e) {
            throw new SecurinestException(HttpStatus.CONFLICT, "Version mismatch");
        } catch (DataIntegrityViolationException e) {
            log.warn("updateTenant constraint issue. requestId={} msg={}", requestId, e.getMessage());
            throw new SecurinestException(HttpStatus.CONFLICT, "Conflict updating tenant");
        }
    }
}
