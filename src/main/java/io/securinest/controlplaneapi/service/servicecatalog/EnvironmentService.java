package io.securinest.controlplaneapi.service.servicecatalog;

import io.securinest.controlplaneapi.dto.servicecatalog.EnvironmentResponse;
import io.securinest.controlplaneapi.mapper.servicecatalog.EnvironmentMapper;
import io.securinest.controlplaneapi.repository.servicecatalog.EnvironmentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class EnvironmentService {

    private final EnvironmentRepository environmentRepository;
    private final EnvironmentMapper environmentMapper;

    public List<EnvironmentResponse> findAllEnvironmentsByTenantId(UUID tenantId) {
        return environmentRepository.findAllByTenantId(tenantId)
                .stream()
                .map(environmentMapper::mapToEnvironmentResponse)
                .toList();
    }

}
