package io.securinest.controlplaneapi.service.policy;

import io.securinest.controlplaneapi.entity.policy.SigningKey;
import io.securinest.controlplaneapi.exceptions.policy.PolicyValidationException;
import io.securinest.controlplaneapi.repository.policy.SigningKeyRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class PolicySigningService {

    private final SigningKeyRepository signingKeyRepository;
    private final SecureRandom secureRandom = new SecureRandom();

    /**
     * Signs a content hash using the active signing key for the tenant.
     * In development, this creates a mock signature.
     * In production, this would integrate with KMS/HSM.
     */
    public SigningResult signContentHash(UUID tenantId, String contentHash) {
        log.debug("Signing content hash for tenant: {}", tenantId);
        
        // Get active signing key for tenant
        SigningKey signingKey = getActiveSigningKey(tenantId)
                .orElseThrow(() -> new PolicyValidationException("No active signing key found for tenant: " + tenantId));
        
        // In development: create mock signature
        // In production: integrate with KMS/HSM
        String signature = createMockSignature(contentHash, signingKey.getKid());
        
        log.debug("Created signature for tenant: {} using key: {}", tenantId, signingKey.getKid());
        
        return new SigningResult(signature, signingKey.getKid());
    }
    
    /**
     * Verifies a signature against a content hash.
     * In development, this performs mock verification.
     * In production, this would use the public key to verify.
     */
    public boolean verifySignature(UUID tenantId, String contentHash, String signatureB64, String kid) {
        log.debug("Verifying signature for tenant: {} with key: {}", tenantId, kid);
        
        // Get signing key by kid
        SigningKey signingKey = signingKeyRepository.findByTenantIdAndKid(tenantId, kid)
                .orElse(null);
        
        if (signingKey == null) {
            log.warn("Signing key not found: {} for tenant: {}", kid, tenantId);
            return false;
        }
        
        // In development: mock verification
        // In production: use public key to verify signature
        String expectedSignature = createMockSignature(contentHash, kid);
        boolean isValid = expectedSignature.equals(signatureB64);
        
        log.debug("Signature verification result: {} for tenant: {}", isValid, tenantId);
        return isValid;
    }
    
    /**
     * Generates a deterministic fingerprint for a public key.
     * This is used as the key ID (kid).
     */
    public String generateKeyFingerprint(String publicKeyPem) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(publicKeyPem.getBytes(StandardCharsets.UTF_8));
            
            // Take first 16 bytes and encode as base32 for shorter kid
            byte[] truncated = new byte[16];
            System.arraycopy(hash, 0, truncated, 0, 16);
            
            return Base64.getUrlEncoder().withoutPadding().encodeToString(truncated);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }
    
    /**
     * Creates a mock signature for development purposes.
     * In production, this would be replaced with actual cryptographic signing.
     */
    private String createMockSignature(String contentHash, String kid) {
        try {
            // Create deterministic signature based on content hash and key ID
            String input = contentHash + ":" + kid + ":mock-secret";
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }
    
    /**
     * Result of a signing operation.
     */
    public record SigningResult(String signatureB64, String kid) {}
    
    // TODO: Production methods for KMS integration
    
    /**
     * Creates a new signing key pair and stores it in KMS.
     * This is a placeholder for production implementation.
     */
    public SigningKey createSigningKey(UUID tenantId) {
        // TODO: Implement KMS key creation
        // 1. Generate key pair in KMS/HSM
        // 2. Get public key and generate fingerprint
        // 3. Store public key and kid in database
        // 4. Return SigningKey entity
        
        throw new UnsupportedOperationException("Key creation not implemented - use development setup");
    }
    
    /**
     * Revokes a signing key in KMS.
     * This is a placeholder for production implementation.
     */
    public void revokeSigningKey(UUID tenantId, String kid) {
        // TODO: Implement KMS key revocation
        // 1. Mark key as revoked in KMS
        // 2. Update database record with revokedAt timestamp
        
        throw new UnsupportedOperationException("Key revocation not implemented - use development setup");
    }

    /**
     * Gets the active signing key for a tenant.
     */
    private Optional<SigningKey> getActiveSigningKey(UUID tenantId) {
        // For now, return the first non-revoked key
        // In production, you'd have logic to determine the "active" key
        List<SigningKey> keys = signingKeyRepository.findAllByTenantIdAndRevokedAtIsNull(tenantId);

        return keys.isEmpty() ? Optional.empty() : Optional.of(keys.get(0));
    }
}
