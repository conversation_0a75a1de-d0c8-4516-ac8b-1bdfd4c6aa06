package io.securinest.controlplaneapi.service;

import io.securinest.controlplaneapi.config.TenantRulesProperties;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import io.securinest.controlplaneapi.util.shared.Constants;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.TreeSet;

@Component
@RequiredArgsConstructor
public class TenantRulebook {

    private final TenantRulesProperties rulesProperties;

    public Set<String> getAllowedRegions() {
        return rulesProperties.getAllowedRegions();
    }

    public Set<String> getReservedSlugs() {
        return rulesProperties.getReservedSlugs();
    }

    public boolean isReservedSlug(String slug) {
        if (slug == null) return false;
        return getReservedSlugs().contains(slug.toLowerCase().trim());
    }

    public void validateName(String name, String fieldName) {
        ValidationUtils.validateName(name, fieldName);
    }

    public void validateSlug(String slug) {
        if (slug == null) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, "Slug cannot be null");
        }

        if (!Constants.SLUG_PATTERN.matcher(slug).matches()) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST,
                    "Slug must be " + Constants.SLUG_MIN_LENGTH + "-" + Constants.SLUG_MAX_LENGTH +
                            " chars, lowercase a-z 0-9 -, no leading/trailing '-', no consecutive '--'");
        }

        if (!Constants.SLUG_HAS_LETTER.matcher(slug).matches()) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST,
                    "Slug must contain at least one letter");
        }

        if (isReservedSlug(slug)) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST,
                    "Slug '" + slug + "' is reserved");
        }
    }

    public void validateRegion(String region) {
        if (region == null) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, "Region cannot be null");
        }

        Set<String> allowedRegions = getAllowedRegions();
        if (!allowedRegions.contains(region.toLowerCase().trim())) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST,
                    "Invalid region. Allowed: " + String.join(", ", new TreeSet<>(allowedRegions)));
        }
    }

    public String normalizeSlug(String rawSlug) {
        return ValidationUtils.normalizeSlug(rawSlug);
    }

    public String normalizeName(String rawName) {
        return ValidationUtils.normalizeName(rawName);
    }

    public String normalizeRegion(String rawRegion) {
        return ValidationUtils.trimToNull(rawRegion);
    }
}
