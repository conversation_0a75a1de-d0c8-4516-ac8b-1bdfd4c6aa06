package io.securinest.controlplaneapi.controller.servicecatalog;

import io.securinest.controlplaneapi.dto.servicecatalog.EnvironmentResponse;
import io.securinest.controlplaneapi.service.servicecatalog.EnvironmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/service-catalogs")
public class ServiceCatalogController {

    private final EnvironmentService environmentService;

    @GetMapping(path = "/tenants/{tenantId}/envs")
    public ResponseEntity<List<EnvironmentResponse>> getAllEnvironmentsByTenantId(@PathVariable UUID tenantId) {
        List<EnvironmentResponse> environmentResponses = environmentService.findAllEnvironmentsByTenantId(tenantId);

        return ResponseEntity.ok(environmentResponses);
    }

}
