package io.securinest.controlplaneapi.controller;

import io.securinest.controlplaneapi.dto.shared.PageResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantCreateRequest;
import io.securinest.controlplaneapi.dto.tenant.TenantResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantUpdateRequest;
import io.securinest.controlplaneapi.service.TenantService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/tenants", produces = "application/json")
public class TenantController {

    private final TenantService tenantService;

    @PostMapping
    public ResponseEntity<TenantResponse> createTenant(
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId,
            @RequestHeader(name = "X-Request-Id", required = false) String requestId,
            @RequestHeader(name = "X-Idempotency-Key", required = false) String idempotencyKey,
            @RequestBody @Valid TenantCreateRequest body
    ) {
        // Dev-only: until auth exists, accept debug user id; otherwise 401 (non-dev profiles TBD)
        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        TenantResponse res = tenantService.createTenant(debugUserId, body, requestId);
        return ResponseEntityUtils.createResponse(res);
    }

    @GetMapping
    public ResponseEntity<PageResponse<TenantResponse>> listMyTenants(
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "size", defaultValue = "20") int size
    ) {
        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        PageResponse<TenantResponse> body = tenantService.listTenantsForUser(debugUserId, page, size);

        return ResponseEntity.ok()
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .body(body);
    }

    @GetMapping(path = "/{tenantId}")
    public ResponseEntity<TenantResponse> getTenant(
            @PathVariable UUID tenantId,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId
    ) {
        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        TenantResponse res = tenantService.getTenant(tenantId, debugUserId);
        return ResponseEntityUtils.getResponse(res);
    }

    @PutMapping(path = "/{tenantId}")
    public ResponseEntity<TenantResponse> updateTenant(
            @PathVariable UUID tenantId,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId,
            @RequestHeader(name = "If-Match", required = false) String ifMatch,
            @RequestHeader(name = "X-Request-Id", required = false) String requestId,
            @RequestBody @Valid TenantUpdateRequest body
    ) {
        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        if (ifMatch == null) {
            return ResponseEntityUtils.preconditionRequired(TenantResponse.class);
        }

        Integer expectedVersion = ValidationUtils.parseIfMatch(ifMatch);
        TenantResponse res = tenantService.updateTenant(tenantId, debugUserId, body, expectedVersion, requestId);

        return ResponseEntityUtils.updateResponse(res);
    }
}
