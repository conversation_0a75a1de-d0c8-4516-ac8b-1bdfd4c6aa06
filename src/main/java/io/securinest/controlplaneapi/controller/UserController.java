package io.securinest.controlplaneapi.controller;

import io.securinest.controlplaneapi.dto.identity.UserAccountCreateRequest;
import io.securinest.controlplaneapi.dto.identity.UserAccountResponse;
import io.securinest.controlplaneapi.projection.identity.UserPatch;
import io.securinest.controlplaneapi.service.UserService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/users", produces = "application/json")
public class UserController {

    private final UserService userService;

    @PostMapping(value = "/signup", consumes = "application/json")
    public ResponseEntity<UserAccountResponse> signUp(
            @RequestHeader(name = "X-Request-Id", required = false) String requestId,
            @RequestBody @Valid UserAccountCreateRequest body
    ) {
        UserAccountResponse response = userService.signUp(body, requestId);
        return ResponseEntityUtils.createResponse(response);
    }

    @GetMapping("/me")
    public ResponseEntity<UserAccountResponse> getMe(
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID userId
    ) {
        if (userId == null) return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();

        UserAccountResponse response = userService.getMe(userId);
        return ResponseEntityUtils.getResponseWeak(response);
    }

    @PutMapping(value = "/me", consumes = "application/json")
    public ResponseEntity<UserAccountResponse> updateMe(
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID userId,
            @RequestHeader(name = "If-Match", required = false) String ifMatch,
            @RequestHeader(name = "X-Request-Id", required = false) String requestId,
            @RequestBody @Valid UserPatch body
    ) {
        if (userId == null) return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        if (ifMatch == null) return ResponseEntityUtils.preconditionRequired(UserAccountResponse.class);

        Integer expectedVersion = ValidationUtils.parseIfMatch(ifMatch);
        UserAccountResponse response = userService.updateMe(userId, body, expectedVersion, requestId);

        return ResponseEntityUtils.updateResponse(response);
    }
}
