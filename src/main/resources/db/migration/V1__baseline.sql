-- Baseline schema for Securinest (single file)
CREATE SCHEMA IF NOT EXISTS securinest;

-- user_account
CREATE TABLE IF NOT EXISTS securinest.user_account(
 id UUID PRIMARY KEY,
 kc_sub VARCHAR(64) NOT NULL UNIQUE,
 email TEXT NOT NULL,
 display_name VA<PERSON>HA<PERSON>(200),
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_user_account_email ON securinest.user_account(email);

-- tenant
CREATE TABLE IF NOT EXISTS securinest.tenant(
 id UUID PRIMARY KEY,
 name VA<PERSON>HA<PERSON>(200) NOT NULL,
 slug VARCHAR(120) NOT NULL,
 billing_plan TEXT NOT NULL DEFAULT 'free',
 region TEXT NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE UNIQUE INDEX uq_tenant_slug_ci ON securinest.tenant(lower(slug));
ALTER TABLE securinest.tenant
  ADD CONSTRAINT chk_tenant_slug_format CHECK (
    slug ~ '^(?!-)(?!.*--)[a-z0-9-]{3,120}(?<!-)$' AND slug ~ '[a-z]'
  );

-- tenant_member
CREATE TABLE IF NOT EXISTS securinest.tenant_member(
 tenant_id UUID NOT NULL,
 user_id UUID NOT NULL,
 role TEXT NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 PRIMARY KEY(tenant_id,user_id)
);
CREATE INDEX IF NOT EXISTS idx_tenant_member_tenant_role ON securinest.tenant_member(tenant_id,role);
CREATE INDEX IF NOT EXISTS idx_tenant_member_user ON securinest.tenant_member(user_id);
ALTER TABLE securinest.tenant_member
  ADD CONSTRAINT chk_member_role CHECK (role IN ('OWNER','ADMIN','POLICY_ADMIN','AUDITOR','VIEWER'));

-- tenant_domain
CREATE TABLE IF NOT EXISTS securinest.tenant_domain(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 domain TEXT NOT NULL,
 verified BOOLEAN NOT NULL DEFAULT FALSE,
 txt_value VARCHAR(120),
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
-- Global uniqueness for verified domains (case-insensitive)
CREATE UNIQUE INDEX IF NOT EXISTS uq_tenant_domain_verified_ci
  ON securinest.tenant_domain (LOWER(domain))
  WHERE verified = TRUE;
-- Index for pending domain lookups
CREATE INDEX IF NOT EXISTS idx_tenant_domain_domain_ci
  ON securinest.tenant_domain (LOWER(domain));
-- Domain format validation
ALTER TABLE securinest.tenant_domain
  ADD CONSTRAINT chk_tenant_domain_format
  CHECK (domain ~ '^[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- tenant_invite
CREATE TABLE IF NOT EXISTS securinest.tenant_invite(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 email TEXT NOT NULL,
 role TEXT NOT NULL,
 token VARCHAR(64) NOT NULL,
 expires_at TIMESTAMPTZ NOT NULL,
 accepted_at TIMESTAMPTZ,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
ALTER TABLE securinest.tenant_invite ADD CONSTRAINT uq_tenant_invite_token UNIQUE(token);
CREATE INDEX IF NOT EXISTS idx_tenant_invite_tenant_email ON securinest.tenant_invite(tenant_id,email);

-- environment
CREATE TABLE IF NOT EXISTS securinest.environment(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 key VARCHAR(32) NOT NULL,
 name VARCHAR(64) NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
ALTER TABLE securinest.environment ADD CONSTRAINT uq_environment_tenant_key UNIQUE(tenant_id,key);
CREATE INDEX IF NOT EXISTS idx_environment_tenant ON securinest.environment(tenant_id);
ALTER TABLE securinest.environment
  ADD CONSTRAINT chk_env_key_format CHECK (key ~ '^[a-z0-9-]{2,32}$');

-- app_service
CREATE TABLE IF NOT EXISTS securinest.app_service(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 name VARCHAR(200) NOT NULL,
 slug VARCHAR(120) NOT NULL,
 description VARCHAR(1000),
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
ALTER TABLE securinest.app_service ADD CONSTRAINT uq_app_service_tenant_slug UNIQUE(tenant_id,slug);
ALTER TABLE securinest.app_service ADD CONSTRAINT uq_app_service_tenant_name UNIQUE(tenant_id,name);
-- Case-insensitive uniqueness per tenant
CREATE UNIQUE INDEX IF NOT EXISTS uq_app_service_tenant_slug_ci
  ON securinest.app_service(tenant_id, LOWER(slug));
CREATE UNIQUE INDEX IF NOT EXISTS uq_app_service_tenant_name_ci
  ON securinest.app_service(tenant_id, LOWER(name));
CREATE INDEX IF NOT EXISTS idx_app_service_tenant ON securinest.app_service(tenant_id);

-- service_environment
CREATE TABLE IF NOT EXISTS securinest.service_environment(
 id UUID PRIMARY KEY,
 service_id UUID NOT NULL,
 env_id UUID NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
ALTER TABLE securinest.service_environment ADD CONSTRAINT uq_service_environment UNIQUE(service_id,env_id);

-- service_api_key
CREATE TABLE IF NOT EXISTS securinest.service_api_key(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 service_id UUID NOT NULL,
 name VARCHAR(120) NOT NULL,
 prefix VARCHAR(64) NOT NULL,
 secret_hash VARCHAR(255) NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL,
 last_used_at TIMESTAMPTZ,
 expires_at TIMESTAMPTZ,
 revoked_at TIMESTAMPTZ
);
ALTER TABLE securinest.service_api_key ADD CONSTRAINT uq_service_api_key_prefix UNIQUE(prefix);
CREATE INDEX IF NOT EXISTS idx_service_api_key_tenant_service ON securinest.service_api_key(tenant_id,service_id);
CREATE INDEX IF NOT EXISTS idx_service_api_key_expires_at ON securinest.service_api_key(expires_at);
CREATE INDEX IF NOT EXISTS idx_service_api_key_revoked_at ON securinest.service_api_key(revoked_at);
CREATE TABLE IF NOT EXISTS securinest.service_api_key_scopes(service_api_key_id UUID NOT NULL, scope TEXT NOT NULL);
ALTER TABLE securinest.service_api_key_scopes
  ADD CONSTRAINT pk_service_api_key_scopes PRIMARY KEY (service_api_key_id, scope);

-- compliance_policy
CREATE TABLE IF NOT EXISTS securinest.compliance_policy(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 name VARCHAR(200) NOT NULL,
 description VARCHAR(2000),
 state TEXT NOT NULL,
 created_by UUID,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_compliance_policy_tenant_state ON securinest.compliance_policy(tenant_id,state);
ALTER TABLE securinest.compliance_policy
  ADD CONSTRAINT chk_policy_state CHECK (state IN ('DRAFT','REVIEW','ACTIVE','ARCHIVED'));

-- compliance_policy_version
CREATE TABLE IF NOT EXISTS securinest.compliance_policy_version(
 id UUID PRIMARY KEY,
 policy_id UUID NOT NULL,
 version_no INT NOT NULL,
 author_id UUID,
 json JSONB NOT NULL,
 status TEXT NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
ALTER TABLE securinest.compliance_policy_version ADD CONSTRAINT uq_policy_version UNIQUE(policy_id,version_no);
ALTER TABLE securinest.compliance_policy_version
  ADD CONSTRAINT chk_policy_version_status CHECK (status IN ('DRAFT','REVIEW','ACTIVE','ARCHIVED'));

-- policy_bundle
CREATE TABLE IF NOT EXISTS securinest.policy_bundle(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 policy_version_id UUID NOT NULL,
 service_id UUID NOT NULL,
 env_id UUID NOT NULL,
 compiled_json JSONB NOT NULL,
 etag VARCHAR(64) NOT NULL,
 signature_b64 TEXT NOT NULL,
 kid VARCHAR(64) NOT NULL,
 active BOOLEAN NOT NULL DEFAULT TRUE,
 enforcement_mode TEXT NOT NULL DEFAULT 'SHADOW',
 lkg_ttl_seconds INTEGER,
 sampling JSONB,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS uq_bundle_active ON securinest.policy_bundle(service_id,env_id) WHERE active=TRUE;
CREATE INDEX IF NOT EXISTS idx_policy_bundle_tenant_service_env_created ON securinest.policy_bundle(tenant_id,service_id,env_id,created_at DESC);

-- signing_key
CREATE TABLE IF NOT EXISTS securinest.signing_key(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 kid TEXT NOT NULL,
 public_key_pem TEXT NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL,
 revoked_at TIMESTAMPTZ
);
ALTER TABLE securinest.signing_key ADD CONSTRAINT uq_signing_key_kid UNIQUE(kid);

-- telemetry_event
CREATE TABLE IF NOT EXISTS securinest.telemetry_event(
 id BIGSERIAL PRIMARY KEY,
 tenant_id UUID NOT NULL,
 service_id UUID NOT NULL,
 env_id UUID NOT NULL,
 ts TIMESTAMPTZ NOT NULL,
 type TEXT NOT NULL,
 endpoint VARCHAR(400),
 policy_ver VARCHAR(60),
 policy_hash VARCHAR(100),
 violation VARCHAR(80),
 latency_ms INTEGER CHECK(latency_ms>=0),
 payload JSONB,
 created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_tel_tenant_ts ON securinest.telemetry_event(tenant_id,ts DESC);
CREATE INDEX IF NOT EXISTS idx_tel_service_ts ON securinest.telemetry_event(service_id,ts DESC);
CREATE INDEX IF NOT EXISTS idx_tel_tenant_type_ts ON securinest.telemetry_event(tenant_id,type,ts DESC);
CREATE INDEX IF NOT EXISTS idx_tel_tenant_service_ts ON securinest.telemetry_event(tenant_id,service_id,ts DESC);
ALTER TABLE securinest.telemetry_event
  ADD CONSTRAINT chk_tel_type CHECK (type IN ('REQUEST','RESPONSE','VIOLATION','OBSERVED_ENDPOINT'));

-- observed_endpoint
CREATE TABLE IF NOT EXISTS securinest.observed_endpoint(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 service_id UUID NOT NULL,
 env_id UUID NOT NULL,
 host VARCHAR(200) NOT NULL,
 path VARCHAR(600) NOT NULL,
 first_seen TIMESTAMPTZ NOT NULL,
 last_seen TIMESTAMPTZ NOT NULL,
 calls BIGINT NOT NULL DEFAULT 0,
 last_status INT,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
ALTER TABLE securinest.observed_endpoint ADD CONSTRAINT uq_observed_endpoint UNIQUE(service_id,env_id,host,path);
CREATE INDEX IF NOT EXISTS idx_observed_endpoint_last_seen ON securinest.observed_endpoint(last_seen DESC);

-- observed_model
CREATE TABLE IF NOT EXISTS securinest.observed_model(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 provider VARCHAR(80) NOT NULL,
 model VARCHAR(120) NOT NULL,
 model_version VARCHAR(80),
 region VARCHAR(40),
 first_seen TIMESTAMPTZ NOT NULL,
 last_seen TIMESTAMPTZ NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS uq_observed_model ON securinest.observed_model(tenant_id,provider,model,COALESCE(model_version,''),COALESCE(region,''));

-- compliance_finding
CREATE TABLE IF NOT EXISTS securinest.compliance_finding(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 service_id UUID NOT NULL,
 env_id UUID NOT NULL,
 code VARCHAR(80) NOT NULL,
 severity TEXT NOT NULL,
 status TEXT NOT NULL DEFAULT 'OPEN',
 details JSONB,
 first_seen TIMESTAMPTZ NOT NULL,
 last_seen TIMESTAMPTZ NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_compliance_finding_tenant_status ON securinest.compliance_finding(tenant_id,status);
CREATE INDEX IF NOT EXISTS idx_compliance_finding_service_env_last_seen ON securinest.compliance_finding(service_id,env_id,last_seen DESC);
ALTER TABLE securinest.compliance_finding
  ADD CONSTRAINT chk_finding_severity CHECK (severity IN ('LOW','MEDIUM','HIGH','CRITICAL')),
  ADD CONSTRAINT chk_finding_status CHECK (status IN ('OPEN','ACKNOWLEDGED','SUPPRESSED','RESOLVED'));

-- usage_daily
CREATE TABLE IF NOT EXISTS securinest.usage_daily(
 tenant_id UUID NOT NULL,
 service_id UUID NOT NULL,
 env_id UUID NOT NULL,
 day DATE NOT NULL,
 events_count BIGINT NOT NULL DEFAULT 0,
 violations_count BIGINT NOT NULL DEFAULT 0,
 bytes_out BIGINT NOT NULL DEFAULT 0,
 PRIMARY KEY(tenant_id,service_id,env_id,day)
);
CREATE INDEX IF NOT EXISTS idx_usage_daily_tenant_day ON securinest.usage_daily(tenant_id,day DESC);

-- scm_account
CREATE TABLE IF NOT EXISTS securinest.scm_account(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 provider TEXT NOT NULL,
 external_installation_id VARCHAR(120) NOT NULL,
 app_slug TEXT,
 webhook_secret TEXT NOT NULL,
 status TEXT NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS uq_scm_account_installation ON securinest.scm_account(tenant_id,provider,external_installation_id);
ALTER TABLE securinest.scm_account
  ADD CONSTRAINT chk_scm_provider CHECK (provider IN ('github','gitlab','bitbucket')),
  ADD CONSTRAINT chk_scm_status CHECK (status IN ('CONNECTED','DISCONNECTED'));

-- scm_repo
CREATE TABLE IF NOT EXISTS securinest.scm_repo(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 scm_account_id UUID NOT NULL,
 external_repo_id VARCHAR(120) NOT NULL,
 name TEXT NOT NULL,
 default_branch TEXT,
 visibility TEXT,
 selected BOOLEAN NOT NULL DEFAULT FALSE,
 last_synced_at TIMESTAMPTZ,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE UNIQUE INDEX IF NOT EXISTS uq_scm_repo_external ON securinest.scm_repo(scm_account_id,external_repo_id);
CREATE INDEX IF NOT EXISTS idx_scm_repo_tenant_selected ON securinest.scm_repo(tenant_id,selected);

-- scm_scan_job
CREATE TABLE IF NOT EXISTS securinest.scm_scan_job(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 triggered_by UUID,
 scope JSONB NOT NULL,
 status TEXT NOT NULL,
 started_at TIMESTAMPTZ,
 finished_at TIMESTAMPTZ,
 counters JSONB,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_scm_scan_job_tenant_started ON securinest.scm_scan_job(tenant_id,started_at DESC);
ALTER TABLE securinest.scm_scan_job
  ADD CONSTRAINT chk_scm_job_status CHECK (status IN ('QUEUED','RUNNING','SUCCEEDED','FAILED','TIMED_OUT'));

-- scm_scan_finding
CREATE TABLE IF NOT EXISTS securinest.scm_scan_finding(
 id UUID PRIMARY KEY,
 scan_job_id UUID NOT NULL,
 tenant_id UUID NOT NULL,
 repo_id UUID NOT NULL,
 type TEXT NOT NULL,
 rule_id TEXT,
 severity TEXT NOT NULL,
 path TEXT NOT NULL,
 line INT,
 snippet_hash TEXT,
 details JSONB,
 commit_sha VARCHAR(64),
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_scm_scan_finding_tenant_type_sev ON securinest.scm_scan_finding(tenant_id,type,severity);

-- policy_suggestion
CREATE TABLE IF NOT EXISTS securinest.policy_suggestion(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 source TEXT NOT NULL,
 service_id UUID,
 repo_id UUID,
 suggestion JSONB NOT NULL,
 confidence DOUBLE PRECISION NOT NULL,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL,
 created_at_ts TIMESTAMPTZ NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_policy_suggestion_tenant_created ON securinest.policy_suggestion(tenant_id,created_at DESC);

-- audit_log_entry
CREATE TABLE IF NOT EXISTS securinest.audit_log_entry(
 id BIGSERIAL PRIMARY KEY,
 tenant_id UUID NOT NULL,
 actor_user_id UUID,
 action TEXT NOT NULL,
 target_type VARCHAR(120) NOT NULL,
 target_id UUID,
 ts TIMESTAMPTZ NOT NULL,
 diff JSONB,
 created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_audit_log_entry_tenant_ts ON securinest.audit_log_entry(tenant_id,ts DESC);
CREATE INDEX IF NOT EXISTS idx_audit_tenant_target_ts ON securinest.audit_log_entry(tenant_id,target_type,ts DESC);

-- audit_pack
CREATE TABLE IF NOT EXISTS securinest.audit_pack(
 id UUID PRIMARY KEY,
 tenant_id UUID NOT NULL,
 scope JSONB NOT NULL,
 status TEXT NOT NULL,
 manifest_hash VARCHAR(80),
 artifact_uri TEXT,
 ai_act_version TEXT,
 created_by UUID,
 created_at TIMESTAMPTZ NOT NULL,
 updated_at TIMESTAMPTZ NOT NULL,
 version BIGINT NOT NULL,
 finished_at TIMESTAMPTZ,
 signature_b64 TEXT
);
CREATE INDEX IF NOT EXISTS idx_audit_pack_tenant_created ON securinest.audit_pack(tenant_id,created_at DESC);

-- FKs
ALTER TABLE securinest.tenant_member
 ADD CONSTRAINT fk_tenant_member_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id),
 ADD CONSTRAINT fk_tenant_member_user FOREIGN KEY(user_id) REFERENCES securinest.user_account(id);
ALTER TABLE securinest.tenant_domain
 ADD CONSTRAINT fk_tenant_domain_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.tenant_invite
 ADD CONSTRAINT fk_tenant_invite_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.environment
 ADD CONSTRAINT fk_env_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.app_service
 ADD CONSTRAINT fk_app_service_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.service_environment
 ADD CONSTRAINT fk_service_environment_service FOREIGN KEY(service_id) REFERENCES securinest.app_service(id),
 ADD CONSTRAINT fk_service_environment_env FOREIGN KEY(env_id) REFERENCES securinest.environment(id);
ALTER TABLE securinest.service_api_key
 ADD CONSTRAINT fk_service_api_key_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id),
 ADD CONSTRAINT fk_service_api_key_service FOREIGN KEY(service_id) REFERENCES securinest.app_service(id);
ALTER TABLE securinest.service_api_key_scopes
 ADD CONSTRAINT fk_service_api_key_scopes_key FOREIGN KEY(service_api_key_id) REFERENCES securinest.service_api_key(id) ON DELETE CASCADE;
ALTER TABLE securinest.compliance_policy
 ADD CONSTRAINT fk_policy_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id),
 ADD CONSTRAINT fk_policy_created_by_user FOREIGN KEY(created_by) REFERENCES securinest.user_account(id);
ALTER TABLE securinest.compliance_policy_version
 ADD CONSTRAINT fk_policy_version_policy FOREIGN KEY(policy_id) REFERENCES securinest.compliance_policy(id),
 ADD CONSTRAINT fk_policy_version_author FOREIGN KEY(author_id) REFERENCES securinest.user_account(id);
ALTER TABLE securinest.policy_bundle
 ADD CONSTRAINT fk_bundle_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id),
 ADD CONSTRAINT fk_bundle_policy_version FOREIGN KEY(policy_version_id) REFERENCES securinest.compliance_policy_version(id),
 ADD CONSTRAINT fk_bundle_service FOREIGN KEY(service_id) REFERENCES securinest.app_service(id),
 ADD CONSTRAINT fk_bundle_env FOREIGN KEY(env_id) REFERENCES securinest.environment(id);
ALTER TABLE securinest.signing_key
 ADD CONSTRAINT fk_signing_key_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.telemetry_event
 ADD CONSTRAINT fk_tel_env FOREIGN KEY(env_id) REFERENCES securinest.environment(id),
 ADD CONSTRAINT fk_tel_service FOREIGN KEY(service_id) REFERENCES securinest.app_service(id),
 ADD CONSTRAINT fk_tel_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.observed_endpoint
 ADD CONSTRAINT fk_observed_endpoint_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.observed_model
 ADD CONSTRAINT fk_observed_model_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.compliance_finding
 ADD CONSTRAINT fk_finding_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.usage_daily
 ADD CONSTRAINT fk_usage_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.scm_account
 ADD CONSTRAINT fk_scm_account_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.scm_repo
 ADD CONSTRAINT fk_scm_repo_account FOREIGN KEY(scm_account_id) REFERENCES securinest.scm_account(id),
 ADD CONSTRAINT fk_scm_repo_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.scm_scan_job
 ADD CONSTRAINT fk_scm_scan_job_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.scm_scan_finding
 ADD CONSTRAINT fk_scm_scan_finding_job FOREIGN KEY(scan_job_id) REFERENCES securinest.scm_scan_job(id),
 ADD CONSTRAINT fk_scm_scan_finding_repo FOREIGN KEY(repo_id) REFERENCES securinest.scm_repo(id),
 ADD CONSTRAINT fk_scm_scan_finding_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.policy_suggestion
 ADD CONSTRAINT fk_policy_suggestion_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id);
ALTER TABLE securinest.audit_log_entry
 ADD CONSTRAINT fk_audit_log_entry_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id),
 ADD CONSTRAINT fk_audit_log_actor_user FOREIGN KEY(actor_user_id) REFERENCES securinest.user_account(id);
ALTER TABLE securinest.audit_pack
 ADD CONSTRAINT fk_audit_pack_tenant FOREIGN KEY(tenant_id) REFERENCES securinest.tenant(id),
 ADD CONSTRAINT fk_audit_pack_created_by FOREIGN KEY(created_by) REFERENCES securinest.user_account(id);


ALTER TABLE securinest.tenant_invite
  ADD COLUMN IF NOT EXISTS token_hash TEXT,
  ADD COLUMN IF NOT EXISTS revoked_at TIMESTAMPTZ;

UPDATE securinest.tenant_invite SET token_hash = token WHERE token_hash IS NULL AND token IS NOT NULL;

DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_constraint c
    JOIN pg_namespace n ON n.oid = c.connamespace
    WHERE c.conname = 'uq_tenant_invite_token' AND n.nspname = 'securinest'
  ) THEN
    ALTER TABLE securinest.tenant_invite DROP CONSTRAINT uq_tenant_invite_token;
  END IF;
END$$;

ALTER TABLE securinest.tenant_invite
  ALTER COLUMN token_hash SET NOT NULL;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes WHERE schemaname = 'securinest' AND indexname = 'uq_tenant_invite_token_hash'
  ) THEN
    CREATE UNIQUE INDEX uq_tenant_invite_token_hash ON securinest.tenant_invite(token_hash);
  END IF;
END$$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes WHERE schemaname = 'securinest' AND indexname = 'idx_tenant_invite_tenant_expires'
  ) THEN
    CREATE INDEX idx_tenant_invite_tenant_expires ON securinest.tenant_invite(tenant_id, expires_at);
  END IF;
END$$;