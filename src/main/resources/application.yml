server:
  port: ${PORT:8080}

spring:
  application:
    name: control-plane-api
  threads:
    virtual:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        default_schema: securinest
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          time_zone: UTC
    open-in-view: false
  datasource:
    url: *******************************************
    username: postgres
    password: admin
  flyway:
    enabled: true
    schemas: securinest
  sql:
    init:
      mode: never
  data:
    redis:
      host: ${REDIS_HOST}
      password: ${REDIS_PASSWORD}
      port: 6379
      database: 0
  docker:
    compose:
      enabled: false

management:
  server:
    port: ${server.port}
  endpoint:
    health:
      group:
        custom:
          show-components: always
          show-details: always
      probes:
        enabled: true
  endpoints:
    web:
      exposure:
        include: health,info,loggers,prometheus
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true

springdoc:
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operationsSorter: method
  api-docs:
    path: /v3/api-docs

logging:
  level:
    org:
      springframework:
        security: ERROR
        web:
          csrf:
            CsrfFilter: ERROR
        jdbc:
          core:
            JdbcTemplate: ERROR
            StatementCreatorUtils: ERROR
      hibernate:
        SQL: ERROR
        type:
          descriptor:
            sql:
              BasicBinder: ERROR

tenant:
  rules:
    allowed-regions:
      - us
      - eu
    extra-reserved-slugs: []
